<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Notice Board - Breaking News & Analysis</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=playfair-display:400,500,600,700|inter:400,500,600" rel="stylesheet" />
    <style>
        .font-serif { font-family: 'Playfair Display', serif; }
        .font-sans { font-family: 'Inter', sans-serif; }

        /* Breaking news animations */
        .breaking-flash {
            animation: flash 2s infinite;
        }

        @keyframes flash {
            0%, 50%, 100% { opacity: 1; }
            25%, 75% { opacity: 0.7; }
        }

        /* Smooth transitions for slider */
        #breakingSlider {
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Hover effects for breaking news */
        .breaking-news-item:hover {
            text-decoration: underline;
            text-decoration-thickness: 2px;
        }

        /* Main Hero Slider Styles */
        .hero-slider-container {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            height: auto;
        }

        .hero-slider {
            display: flex;
            transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            width: 100%;
        }

        .hero-slide {
            min-width: 100%;
            flex-shrink: 0;
            position: relative;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            aspect-ratio: 16/10;
            display: flex;
            align-items: flex-end;
        }

        .hero-slide-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent 0%, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.8) 100%);
            padding: 2rem;
            color: white;
            width: 100%;
            box-sizing: border-box;
        }

        .hero-slide-category {
            display: inline-block;
            background: #dc2626;
            color: white;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            border-radius: 0.25rem;
            margin-bottom: 0.75rem;
        }

        .hero-slide-title {
            font-size: 1.875rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 0.75rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .hero-slide-excerpt {
            font-size: 1rem;
            line-height: 1.5;
            opacity: 0.9;
            margin-bottom: 0.75rem;
        }

        .hero-slide-meta {
            font-size: 0.875rem;
            opacity: 0.8;
        }

        /* Navigation Controls */
        .hero-nav-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.95);
            border: none;
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 20;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }

        .hero-nav-btn:hover {
            background: white;
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }

        .hero-nav-btn:active {
            transform: translateY(-50%) scale(0.95);
        }

        .hero-nav-btn.prev {
            left: 1rem;
        }

        .hero-nav-btn.next {
            right: 1rem;
        }

        /* Slide Indicators */
        .hero-indicators {
            position: absolute;
            bottom: 1.5rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 0.75rem;
            z-index: 20;
            padding: 0.5rem 1rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 2rem;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }

        .hero-indicator {
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .hero-indicator.active {
            background: white;
            transform: scale(1.3);
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
        }

        .hero-indicator:hover:not(.active) {
            background: rgba(255, 255, 255, 0.8);
            transform: scale(1.1);
        }

        /* Enhanced animations */
        @keyframes slideInFromRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInFromBottom {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in-right {
            animation: slideInFromRight 0.6s ease-out;
        }

        .slide-in-bottom {
            animation: slideInFromBottom 0.8s ease-out;
        }

        /* Responsive adjustments */
        @media (max-width: 1280px) {
            .hero-slide-title {
                font-size: 1.75rem;
            }

            .hero-slide-overlay {
                padding: 1.75rem;
            }
        }

        @media (max-width: 1024px) {
            /* Tablet layout adjustments */
            .hero-slide-title {
                font-size: 1.625rem;
                line-height: 1.25;
            }

            .hero-slide-overlay {
                padding: 1.5rem;
            }

            .hero-slide-excerpt {
                font-size: 0.9375rem;
                line-height: 1.5;
            }

            .hero-nav-btn {
                width: 2.75rem;
                height: 2.75rem;
            }
        }

        @media (max-width: 768px) {
            /* Mobile layout - single column */
            .hero-slide-title {
                font-size: 1.5rem;
                line-height: 1.3;
            }

            .hero-slide-overlay {
                padding: 1.25rem;
            }

            .hero-nav-btn {
                width: 2.5rem;
                height: 2.5rem;
            }

            .hero-nav-btn.prev {
                left: 0.75rem;
            }

            .hero-nav-btn.next {
                right: 0.75rem;
            }

            .hero-slide-excerpt {
                font-size: 0.875rem;
                line-height: 1.4;
                margin-bottom: 0.5rem;
            }

            .hero-slide-meta {
                font-size: 0.75rem;
            }

            .hero-indicators {
                bottom: 1rem;
                gap: 0.5rem;
                padding: 0.375rem 0.75rem;
            }

            .hero-indicator {
                width: 0.625rem;
                height: 0.625rem;
            }

            .hero-indicator.active {
                transform: scale(1.2);
            }
        }

        @media (max-width: 640px) {
            /* Small mobile adjustments */
            .hero-slide-title {
                font-size: 1.25rem;
                line-height: 1.3;
                margin-bottom: 0.5rem;
            }

            .hero-slide-overlay {
                padding: 1rem;
            }

            .hero-slide-excerpt {
                display: none;
            }

            .hero-slide-category {
                font-size: 0.6875rem;
                padding: 0.1875rem 0.5rem;
                margin-bottom: 0.5rem;
            }

            .hero-nav-btn {
                width: 2.25rem;
                height: 2.25rem;
            }

            .hero-nav-btn.prev {
                left: 0.5rem;
            }

            .hero-nav-btn.next {
                right: 0.5rem;
            }

            .hero-indicators {
                bottom: 0.75rem;
                gap: 0.375rem;
                padding: 0.25rem 0.5rem;
            }

            .hero-indicator {
                width: 0.5rem;
                height: 0.5rem;
            }

            .hero-indicator.active {
                transform: scale(1.15);
            }
        }

        @media (max-width: 480px) {
            /* Extra small mobile */
            .hero-slide-title {
                font-size: 1.125rem;
            }

            .hero-slide-overlay {
                padding: 0.75rem;
            }

            .hero-slide-meta {
                font-size: 0.6875rem;
            }
        }

        /* Enhanced hover effects */
        .article-card-hover {
            transition: all 0.3s ease;
        }

        .article-card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .image-zoom {
            transition: transform 0.3s ease;
        }

        .image-zoom:hover {
            transform: scale(1.05);
        }

        /* Cross-browser compatibility fixes */
        .hero-slider-container {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            will-change: transform;
        }

        .hero-slider {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            will-change: transform;
        }

        .hero-slide {
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }

        /* Fix for Safari aspect-ratio support */
        @supports not (aspect-ratio: 16/10) {
            .hero-slide {
                height: 0;
                padding-bottom: 62.5%; /* 10/16 = 0.625 */
            }

            .hero-slide-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
            }
        }

        /* Ensure proper stacking context */
        .hero-slider-container {
            isolation: isolate;
        }
    </style>
</head>
<body class="font-sans antialiased">
    <!-- Header -->
    <header class="border-b border-gray-200">
        <!-- Breaking News Slider -->
        <div class="bg-red-600 text-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center py-2">
                    <div class="flex items-center space-x-3 mr-4">
                        <span class="bg-white text-red-600 px-2 py-1 text-xs font-bold uppercase rounded breaking-flash">
                            Breaking
                        </span>
                        <div class="flex space-x-2">
                            <button id="breakingPrevBtn" class="p-1 rounded-full hover:bg-red-700 transition-colors duration-200">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </button>
                            <button id="breakingNextBtn" class="p-1 rounded-full hover:bg-red-700 transition-colors duration-200">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="flex-1 overflow-hidden">
                        <div id="breakingSlider" class="flex">
                            <div class="w-full flex-shrink-0">
                                <a href="#" class="text-sm breaking-news-item">
                                    Major Technology Breakthrough Changes Everything We Know About Computing
                                </a>
                            </div>
                            <div class="w-full flex-shrink-0">
                                <a href="#" class="text-sm breaking-news-item">
                                    Global Markets Surge Following Historic Economic Agreement Between Nations
                                </a>
                            </div>
                            <div class="w-full flex-shrink-0">
                                <a href="#" class="text-sm breaking-news-item">
                                    Climate Summit Reaches Unprecedented Deal on Carbon Emissions Reduction
                                </a>
                            </div>
                            <div class="w-full flex-shrink-0">
                                <a href="#" class="text-sm breaking-news-item">
                                    Revolutionary Medical Treatment Shows 95% Success Rate in Clinical Trials
                                </a>
                            </div>
                            <div class="w-full flex-shrink-0">
                                <a href="#" class="text-sm breaking-news-item">
                                    Space Mission Discovers Potential Signs of Life on Distant Planet
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top bar with date and weather -->
        <div class="bg-gray-50 border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-2 text-sm text-gray-600">
                    <div class="flex items-center space-x-4">
                        <span id="current-date"></span>
                        <span class="hidden sm:inline">Today's Paper</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="hidden md:inline">Subscribe</span>
                        <span>Log In</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main header -->
        <div class="bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <!-- Mobile menu button -->
                    <button class="md:hidden p-2">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>

                    <!-- Logo -->
                    <div class="flex-1 flex justify-center md:justify-start">
                        <h1 class="text-3xl md:text-4xl font-serif font-bold text-black tracking-tight">
                            The Notice Board
                        </h1>
                    </div>

                    <!-- Search and subscribe -->
                    <div class="hidden md:flex items-center space-x-4">
                        <button class="p-2 hover:bg-gray-100 rounded">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                        <button class="bg-blue-600 text-white px-4 py-2 text-sm font-medium rounded hover:bg-blue-700">
                            Subscribe
                        </button>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="border-t border-gray-200">
                    <div class="flex justify-center">
                        <div class="flex space-x-8 overflow-x-auto py-3">
                            <a href="#" class="text-sm font-medium text-gray-900 hover:text-blue-600 whitespace-nowrap">World</a>
                            <a href="#" class="text-sm font-medium text-gray-900 hover:text-blue-600 whitespace-nowrap">U.S.</a>
                            <a href="#" class="text-sm font-medium text-gray-900 hover:text-blue-600 whitespace-nowrap">Politics</a>
                            <a href="#" class="text-sm font-medium text-gray-900 hover:text-blue-600 whitespace-nowrap">Business</a>
                            <a href="#" class="text-sm font-medium text-gray-900 hover:text-blue-600 whitespace-nowrap">Technology</a>
                            <a href="#" class="text-sm font-medium text-gray-900 hover:text-blue-600 whitespace-nowrap">Science</a>
                            <a href="#" class="text-sm font-medium text-gray-900 hover:text-blue-600 whitespace-nowrap">Health</a>
                            <a href="#" class="text-sm font-medium text-gray-900 hover:text-blue-600 whitespace-nowrap">Sports</a>
                            <a href="#" class="text-sm font-medium text-gray-900 hover:text-blue-600 whitespace-nowrap">Arts</a>
                            <a href="#" class="text-sm font-medium text-gray-900 hover:text-blue-600 whitespace-nowrap">Opinion</a>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <!-- Enhanced Hero Section -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8 items-start">
            <!-- Main Hero Slider -->
            <div class="lg:col-span-7">
                <div class="hero-slider-container">
                    <div id="heroSlider" class="hero-slider">
                        <!-- Slide 1 -->
                        <div class="hero-slide" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');">
                            <div class="hero-slide-overlay">
                                <span class="hero-slide-category">Technology</span>
                                <h1 class="hero-slide-title font-serif">
                                    Revolutionary AI Breakthrough Changes Computing Forever
                                </h1>
                                <p class="hero-slide-excerpt">
                                    Scientists unveil quantum-AI hybrid system that promises to solve complex problems in seconds, marking a new era in computational power and artificial intelligence.
                                </p>
                                <div class="hero-slide-meta">
                                    By Dr. Sarah Chen • 2 hours ago • 8 min read
                                </div>
                            </div>
                        </div>

                        <!-- Slide 2 -->
                        <div class="hero-slide" style="background-image: url('https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');">
                            <div class="hero-slide-overlay">
                                <span class="hero-slide-category">Environment</span>
                                <h1 class="hero-slide-title font-serif">
                                    Global Climate Summit Reaches Historic Agreement
                                </h1>
                                <p class="hero-slide-excerpt">
                                    World leaders unite on unprecedented climate action plan, committing to carbon neutrality by 2040 and massive renewable energy investments.
                                </p>
                                <div class="hero-slide-meta">
                                    By Environmental Desk • 4 hours ago • 6 min read
                                </div>
                            </div>
                        </div>

                        <!-- Slide 3 -->
                        <div class="hero-slide" style="background-image: url('https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');">
                            <div class="hero-slide-overlay">
                                <span class="hero-slide-category">Business</span>
                                <h1 class="hero-slide-title font-serif">
                                    Markets Soar as Tech Giants Report Record Earnings
                                </h1>
                                <p class="hero-slide-excerpt">
                                    Major technology companies exceed expectations with unprecedented quarterly results, driving global markets to new heights amid economic uncertainty.
                                </p>
                                <div class="hero-slide-meta">
                                    By Financial Team • 5 hours ago • 7 min read
                                </div>
                            </div>
                        </div>

                        <!-- Slide 4 -->
                        <div class="hero-slide" style="background-image: url('https://images.unsplash.com/photo-1446776877081-d282a0f896e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');">
                            <div class="hero-slide-overlay">
                                <span class="hero-slide-category">Science</span>
                                <h1 class="hero-slide-title font-serif">
                                    Space Mission Discovers Signs of Ancient Life
                                </h1>
                                <p class="hero-slide-excerpt">
                                    NASA's latest Mars rover uncovers compelling evidence of microbial life that existed billions of years ago, reshaping our understanding of life in the universe.
                                </p>
                                <div class="hero-slide-meta">
                                    By Space Correspondent • 6 hours ago • 9 min read
                                </div>
                            </div>
                        </div>

                        <!-- Slide 5 -->
                        <div class="hero-slide" style="background-image: url('https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');">
                            <div class="hero-slide-overlay">
                                <span class="hero-slide-category">Health</span>
                                <h1 class="hero-slide-title font-serif">
                                    Medical Breakthrough Offers Hope for Cancer Patients
                                </h1>
                                <p class="hero-slide-excerpt">
                                    Revolutionary gene therapy shows 95% success rate in clinical trials, offering new hope for patients with previously untreatable forms of cancer.
                                </p>
                                <div class="hero-slide-meta">
                                    By Medical Correspondent • 8 hours ago • 10 min read
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Controls -->
                    <button id="heroPrevBtn" class="hero-nav-btn prev">
                        <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <button id="heroNextBtn" class="hero-nav-btn next">
                        <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>

                    <!-- Slide Indicators -->
                    <div class="hero-indicators">
                        <button class="hero-indicator active" data-slide="0"></button>
                        <button class="hero-indicator" data-slide="1"></button>
                        <button class="hero-indicator" data-slide="2"></button>
                        <button class="hero-indicator" data-slide="3"></button>
                        <button class="hero-indicator" data-slide="4"></button>
                    </div>
                </div>
            </div>

            <!-- Enhanced Sidebar -->
            <div class="lg:col-span-5 space-y-6">
                <div class="border-b border-gray-200 pb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-serif font-bold text-black border-b border-black pb-2">
                            Latest Updates
                        </h2>
                        <div class="flex space-x-2">
                            <button id="prevBtn" class="p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200">
                                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </button>
                            <button id="nextBtn" class="p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200">
                                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div class="relative overflow-hidden">
                        <div id="newsSlider" class="flex transition-transform duration-500 ease-in-out">
                            <!-- Slide 1 -->
                            <div class="w-full flex-shrink-0 space-y-4">
                                <article class="group cursor-pointer">
                                    <div class="flex space-x-3">
                                        <img
                                            src="https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                            alt="Global Markets"
                                            class="w-20 h-20 object-cover flex-shrink-0 rounded"
                                        />
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                                Global Markets React to Economic Policy Changes
                                            </h3>
                                            <p class="text-xs text-gray-600 mb-1">
                                                New partnerships emerge as nations seek economic stability.
                                            </p>
                                            <p class="text-xs text-gray-500">3 hours ago</p>
                                        </div>
                                    </div>
                                </article>

                                <article class="group cursor-pointer">
                                    <div class="flex space-x-3">
                                        <img
                                            src="https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                            alt="Climate Summit"
                                            class="w-20 h-20 object-cover flex-shrink-0 rounded"
                                        />
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                                Climate Summit Reaches Historic Agreement
                                            </h3>
                                            <p class="text-xs text-gray-600 mb-1">
                                                World leaders unite on environmental action plan.
                                            </p>
                                            <p class="text-xs text-gray-500">5 hours ago</p>
                                        </div>
                                    </div>
                                </article>

                                <article class="group cursor-pointer">
                                    <div class="flex space-x-3">
                                        <img
                                            src="https://images.unsplash.com/photo-1446776877081-d282a0f896e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                            alt="Space Mission"
                                            class="w-20 h-20 object-cover flex-shrink-0 rounded"
                                        />
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                                Space Exploration Mission Achieves New Milestone
                                            </h3>
                                            <p class="text-xs text-gray-600 mb-1">
                                                Breakthrough discoveries expand our understanding of the universe.
                                            </p>
                                            <p class="text-xs text-gray-500">6 hours ago</p>
                                        </div>
                                    </div>
                                </article>
                            </div>

                            <!-- Slide 2 -->
                            <div class="w-full flex-shrink-0 space-y-4">
                                <article class="group cursor-pointer">
                                    <div class="flex space-x-3">
                                        <img
                                            src="https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                            alt="Tech Innovation"
                                            class="w-20 h-20 object-cover flex-shrink-0 rounded"
                                        />
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                                Revolutionary AI System Transforms Healthcare
                                            </h3>
                                            <p class="text-xs text-gray-600 mb-1">
                                                New diagnostic tools promise faster, more accurate results.
                                            </p>
                                            <p class="text-xs text-gray-500">8 hours ago</p>
                                        </div>
                                    </div>
                                </article>

                                <article class="group cursor-pointer">
                                    <div class="flex space-x-3">
                                        <img
                                            src="https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                            alt="Education"
                                            class="w-20 h-20 object-cover flex-shrink-0 rounded"
                                        />
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                                Education Reform Initiative Gains Global Support
                                            </h3>
                                            <p class="text-xs text-gray-600 mb-1">
                                                International coalition commits to improving access to quality education.
                                            </p>
                                            <p class="text-xs text-gray-500">10 hours ago</p>
                                        </div>
                                    </div>
                                </article>

                                <article class="group cursor-pointer">
                                    <div class="flex space-x-3">
                                        <img
                                            src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                            alt="Data Analytics"
                                            class="w-20 h-20 object-cover flex-shrink-0 rounded"
                                        />
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                                Big Data Analytics Reveals Consumer Trends
                                            </h3>
                                            <p class="text-xs text-gray-600 mb-1">
                                                New insights help businesses adapt to changing market demands.
                                            </p>
                                            <p class="text-xs text-gray-500">12 hours ago</p>
                                        </div>
                                    </div>
                                </article>
                            </div>

                            <!-- Slide 3 -->
                            <div class="w-full flex-shrink-0 space-y-4">
                                <article class="group cursor-pointer">
                                    <div class="flex space-x-3">
                                        <img
                                            src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                            alt="Medical Research"
                                            class="w-20 h-20 object-cover flex-shrink-0 rounded"
                                        />
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                                Breakthrough Cancer Treatment Shows Promise
                                            </h3>
                                            <p class="text-xs text-gray-600 mb-1">
                                                Clinical trials demonstrate significant improvement in patient outcomes.
                                            </p>
                                            <p class="text-xs text-gray-500">14 hours ago</p>
                                        </div>
                                    </div>
                                </article>

                                <article class="group cursor-pointer">
                                    <div class="flex space-x-3">
                                        <img
                                            src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                            alt="Renewable Energy"
                                            class="w-20 h-20 object-cover flex-shrink-0 rounded"
                                        />
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                                Solar Energy Efficiency Reaches Record High
                                            </h3>
                                            <p class="text-xs text-gray-600 mb-1">
                                                New technology makes renewable energy more accessible and affordable.
                                            </p>
                                            <p class="text-xs text-gray-500">16 hours ago</p>
                                        </div>
                                    </div>
                                </article>

                                <article class="group cursor-pointer">
                                    <div class="flex space-x-3">
                                        <img
                                            src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                            alt="Urban Development"
                                            class="w-20 h-20 object-cover flex-shrink-0 rounded"
                                        />
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                                Smart Cities Initiative Transforms Urban Living
                                            </h3>
                                            <p class="text-xs text-gray-600 mb-1">
                                                IoT integration improves efficiency and quality of life for residents.
                                            </p>
                                            <p class="text-xs text-gray-500">18 hours ago</p>
                                        </div>
                                    </div>
                                </article>
                            </div>
                        </div>

                        <!-- Slide indicators -->
                        <div class="flex justify-center mt-4 space-x-2">
                            <button class="slide-indicator w-2 h-2 rounded-full bg-blue-600 transition-colors duration-200" data-slide="0"></button>
                            <button class="slide-indicator w-2 h-2 rounded-full bg-gray-300 hover:bg-gray-400 transition-colors duration-200" data-slide="1"></button>
                            <button class="slide-indicator w-2 h-2 rounded-full bg-gray-300 hover:bg-gray-400 transition-colors duration-200" data-slide="2"></button>
                        </div>
                    </div>
                </div>

                <!-- Opinion section -->
                <div>
                    <h2 class="text-lg font-serif font-bold text-black mb-4 border-b border-black pb-2">
                        Opinion
                    </h2>
                    <div class="space-y-3">
                        <article class="group cursor-pointer">
                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                The Future of Work in a Digital Age
                            </h3>
                            <p class="text-xs text-gray-600">
                                By Michael Chen
                            </p>
                        </article>
                        
                        <article class="group cursor-pointer">
                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                Why Education Reform Cannot Wait
                            </h3>
                            <p class="text-xs text-gray-600">
                                By Dr. Emily Rodriguez
                            </p>
                        </article>
                        
                        <article class="group cursor-pointer">
                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                                The Path Forward for Healthcare
                            </h3>
                            <p class="text-xs text-gray-600">
                                By James Wilson
                            </p>
                        </article>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Article Grid Section -->
    <section class="bg-gray-50 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section headers -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- World Section -->
                <div class="border-b border-gray-300 pb-4">
                    <h2 class="text-xl font-serif font-bold text-black mb-4">World</h2>
                    <div class="space-y-4">
                        <article class="group cursor-pointer">
                            <div class="flex space-x-3">
                                <img
                                    src="https://images.unsplash.com/photo-1526304640581-d334cdbbf45e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                    alt="International Trade"
                                    class="w-24 h-24 object-cover flex-shrink-0"
                                />
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                                        International Trade Agreements Reshape Global Economy
                                    </h3>
                                    <p class="text-xs text-gray-600 mb-1">
                                        New partnerships emerge as nations seek economic stability.
                                    </p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>Anna Martinez</span>
                                        <span>•</span>
                                        <span>4 hours ago</span>
                                    </div>
                                </div>
                            </div>
                        </article>

                        <article class="group cursor-pointer">
                            <div class="flex space-x-3">
                                <img
                                    src="https://images.unsplash.com/photo-1529107386315-e1a2ed48a620?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                    alt="Diplomatic Relations"
                                    class="w-24 h-24 object-cover flex-shrink-0"
                                />
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                                        Diplomatic Relations Strengthen Between Key Nations
                                    </h3>
                                    <p class="text-xs text-gray-600 mb-1">
                                        Historic meetings pave way for future cooperation.
                                    </p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>Robert Kim</span>
                                        <span>•</span>
                                        <span>6 hours ago</span>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </div>
                </div>

                <!-- Business Section -->
                <div class="border-b border-gray-300 pb-4">
                    <h2 class="text-xl font-serif font-bold text-black mb-4">Business</h2>
                    <div class="space-y-4">
                        <article class="group cursor-pointer">
                            <div class="flex space-x-3">
                                <img
                                    src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                    alt="Tech Earnings"
                                    class="w-24 h-24 object-cover flex-shrink-0"
                                />
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                                        Tech Giants Report Record Quarterly Earnings
                                    </h3>
                                    <p class="text-xs text-gray-600 mb-1">
                                        Innovation drives unprecedented growth across sectors.
                                    </p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>Lisa Thompson</span>
                                        <span>•</span>
                                        <span>3 hours ago</span>
                                    </div>
                                </div>
                            </div>
                        </article>

                        <article class="group cursor-pointer">
                            <div class="flex space-x-3">
                                <img
                                    src="https://images.unsplash.com/photo-1466611653911-95081537e5b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                    alt="Sustainable Energy"
                                    class="w-24 h-24 object-cover flex-shrink-0"
                                />
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                                        Sustainable Energy Investments Reach New Heights
                                    </h3>
                                    <p class="text-xs text-gray-600 mb-1">
                                        Green technology attracts billions in funding.
                                    </p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>David Park</span>
                                        <span>•</span>
                                        <span>5 hours ago</span>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </div>
                </div>

                <!-- Science Section -->
                <div class="border-b border-gray-300 pb-4">
                    <h2 class="text-xl font-serif font-bold text-black mb-4">Science</h2>
                    <div class="space-y-4">
                        <article class="group cursor-pointer">
                            <div class="flex space-x-3">
                                <img
                                    src="https://images.unsplash.com/photo-1635070041078-e363dbe005cb?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                    alt="Quantum Computing"
                                    class="w-24 h-24 object-cover flex-shrink-0"
                                />
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                                        Breakthrough in Quantum Computing Research
                                    </h3>
                                    <p class="text-xs text-gray-600 mb-1">
                                        Scientists achieve new milestone in quantum supremacy.
                                    </p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>Dr. Rachel Green</span>
                                        <span>•</span>
                                        <span>7 hours ago</span>
                                    </div>
                                </div>
                            </div>
                        </article>

                        <article class="group cursor-pointer">
                            <div class="flex space-x-3">
                                <img
                                    src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                                    alt="Medical Research"
                                    class="w-24 h-24 object-cover flex-shrink-0"
                                />
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                                        Medical Advances Promise Better Treatment Options
                                    </h3>
                                    <p class="text-xs text-gray-600 mb-1">
                                        New therapies show promising results in clinical trials.
                                    </p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>Dr. Mark Johnson</span>
                                        <span>•</span>
                                        <span>8 hours ago</span>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </div>
                </div>
            </div>

            <!-- Featured articles grid -->
            <div class="border-t border-gray-300 pt-12">
                <h2 class="text-2xl font-serif font-bold text-black mb-8 text-center">
                    More Stories
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <article class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden group cursor-pointer">
                        <div class="aspect-w-16 aspect-h-9">
                            <img
                                src="https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                alt="AI in Healthcare"
                                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
                            />
                        </div>
                        <div class="p-4">
                            <div class="flex items-center space-x-2 text-xs text-gray-600 mb-2">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
                                    Health
                                </span>
                                <span>1 day ago</span>
                            </div>
                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                                The Rise of Artificial Intelligence in Healthcare
                            </h3>
                            <p class="text-xs text-gray-600 mb-3">
                                How AI is transforming medical diagnosis and treatment.
                            </p>
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <span>Dr. Sarah Wilson</span>
                                <span>8 min</span>
                            </div>
                        </div>
                    </article>

                    <article class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden group cursor-pointer">
                        <div class="aspect-w-16 aspect-h-9">
                            <img
                                src="https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                alt="Climate Solutions"
                                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
                            />
                        </div>
                        <div class="p-4">
                            <div class="flex items-center space-x-2 text-xs text-gray-600 mb-2">
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                                    Environment
                                </span>
                                <span>1 day ago</span>
                            </div>
                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                                Climate Change Solutions Gain Momentum Worldwide
                            </h3>
                            <p class="text-xs text-gray-600 mb-3">
                                Innovative approaches to environmental challenges show promise.
                            </p>
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <span>Environmental Team</span>
                                <span>6 min</span>
                            </div>
                        </div>
                    </article>

                    <article class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden group cursor-pointer">
                        <div class="aspect-w-16 aspect-h-9">
                            <img
                                src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                alt="Sports Technology"
                                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
                            />
                        </div>
                        <div class="p-4">
                            <div class="flex items-center space-x-2 text-xs text-gray-600 mb-2">
                                <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs font-medium">
                                    Sports
                                </span>
                                <span>2 days ago</span>
                            </div>
                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                                Sports Technology Revolutionizes Athletic Performance
                            </h3>
                            <p class="text-xs text-gray-600 mb-3">
                                Advanced analytics and wearables change the game.
                            </p>
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <span>Sports Desk</span>
                                <span>5 min</span>
                            </div>
                        </div>
                    </article>

                    <article class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden group cursor-pointer">
                        <div class="aspect-w-16 aspect-h-9">
                            <img
                                src="https://images.unsplash.com/photo-1541961017774-22349e4a1262?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                alt="Digital Art"
                                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
                            />
                        </div>
                        <div class="p-4">
                            <div class="flex items-center space-x-2 text-xs text-gray-600 mb-2">
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-medium">
                                    Arts
                                </span>
                                <span>2 days ago</span>
                            </div>
                            <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                                Cultural Renaissance in Digital Art Forms
                            </h3>
                            <p class="text-xs text-gray-600 mb-3">
                                Artists embrace technology to create new expressions.
                            </p>
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <span>Arts & Culture</span>
                                <span>7 min</span>
                            </div>
                        </div>
                    </article>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Main footer content -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
                <!-- Company info -->
                <div class="lg:col-span-1">
                    <h3 class="text-xl font-serif font-bold mb-4">The Notice Board</h3>
                    <p class="text-gray-300 text-sm mb-4">
                        Your trusted source for breaking news, in-depth analysis, and comprehensive coverage of events that matter.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-white">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Navigation links -->
                <div class="grid grid-cols-2 gap-8 lg:col-span-3">
                    <div>
                        <h4 class="text-sm font-semibold uppercase tracking-wider mb-4">News</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">World</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">U.S.</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Politics</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Business</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Technology</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold uppercase tracking-wider mb-4">Opinion</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Today's Opinion</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Op-Ed Columnists</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Editorials</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Op-Ed Contributors</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Letters</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold uppercase tracking-wider mb-4">Arts</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Today's Arts</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Art & Design</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Books</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Dance</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Movies</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold uppercase tracking-wider mb-4">Living</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Automobiles</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Games</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Education</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Food</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-white text-sm">Health</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Newsletter signup -->
            <div class="border-t border-gray-700 pt-8 mb-8">
                <div class="max-w-md">
                    <h4 class="text-lg font-semibold mb-2">Stay Informed</h4>
                    <p class="text-gray-300 text-sm mb-4">Get the latest news delivered to your inbox.</p>
                    <div class="flex">
                        <input
                            type="email"
                            placeholder="Enter your email"
                            class="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-l-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <button class="bg-blue-600 text-white px-6 py-2 rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800">
                            Subscribe
                        </button>
                    </div>
                </div>
            </div>

            <!-- Bottom footer -->
            <div class="border-t border-gray-700 pt-8 flex flex-col md:flex-row justify-between items-center">
                <div class="flex space-x-6 mb-4 md:mb-0">
                    <a href="#" class="text-gray-300 hover:text-white text-sm">Privacy Policy</a>
                    <a href="#" class="text-gray-300 hover:text-white text-sm">Terms of Service</a>
                    <a href="#" class="text-gray-300 hover:text-white text-sm">Contact Us</a>
                    <a href="#" class="text-gray-300 hover:text-white text-sm">Accessibility</a>
                </div>
                <p class="text-gray-400 text-sm">
                    © <span id="current-year"></span> The Notice Board. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Set current date
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Set current year
        document.getElementById('current-year').textContent = new Date().getFullYear();

        // Breaking News Slider
        let breakingCurrentSlide = 0;
        const breakingSlider = document.getElementById('breakingSlider');
        const breakingSlides = breakingSlider.children;
        const breakingTotalSlides = breakingSlides.length;

        function updateBreakingSlider() {
            const translateX = -breakingCurrentSlide * 100;
            breakingSlider.style.transform = `translateX(${translateX}%)`;
        }

        function nextBreakingSlide() {
            breakingCurrentSlide = (breakingCurrentSlide + 1) % breakingTotalSlides;
            updateBreakingSlider();
        }

        function prevBreakingSlide() {
            breakingCurrentSlide = (breakingCurrentSlide - 1 + breakingTotalSlides) % breakingTotalSlides;
            updateBreakingSlider();
        }

        // Breaking news navigation
        document.getElementById('breakingNextBtn').addEventListener('click', nextBreakingSlide);
        document.getElementById('breakingPrevBtn').addEventListener('click', prevBreakingSlide);

        // Auto-advance breaking news every 5 seconds
        setInterval(nextBreakingSlide, 5000);

        // Pause auto-advance on hover
        const breakingContainer = document.querySelector('.bg-red-600');
        let breakingAutoAdvance = true;

        breakingContainer.addEventListener('mouseenter', () => {
            breakingAutoAdvance = false;
        });

        breakingContainer.addEventListener('mouseleave', () => {
            breakingAutoAdvance = true;
        });

        // Modified auto-advance to respect hover state
        setInterval(() => {
            if (breakingAutoAdvance) {
                nextBreakingSlide();
            }
        }, 5000);

        // Main Hero Slider
        let heroCurrentSlide = 0;
        const heroSlider = document.getElementById('heroSlider');
        const heroSlides = heroSlider.children;
        const heroTotalSlides = heroSlides.length;
        const heroIndicators = document.querySelectorAll('.hero-indicator');
        let heroAutoAdvance = true;

        function updateHeroSlider() {
            const translateX = -heroCurrentSlide * 100;
            heroSlider.style.transform = `translateX(${translateX}%)`;

            // Update indicators
            heroIndicators.forEach((indicator, index) => {
                indicator.classList.toggle('active', index === heroCurrentSlide);
            });
        }

        function nextHeroSlide() {
            heroCurrentSlide = (heroCurrentSlide + 1) % heroTotalSlides;
            updateHeroSlider();
        }

        function prevHeroSlide() {
            heroCurrentSlide = (heroCurrentSlide - 1 + heroTotalSlides) % heroTotalSlides;
            updateHeroSlider();
        }

        function goToHeroSlide(slideIndex) {
            heroCurrentSlide = slideIndex;
            updateHeroSlider();
        }

        // Hero slider navigation
        document.getElementById('heroNextBtn').addEventListener('click', nextHeroSlide);
        document.getElementById('heroPrevBtn').addEventListener('click', prevHeroSlide);

        // Hero slider indicators
        heroIndicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => goToHeroSlide(index));
        });

        // Auto-advance hero slider every 7 seconds
        setInterval(() => {
            if (heroAutoAdvance) {
                nextHeroSlide();
            }
        }, 7000);

        // Pause hero auto-advance on hover
        const heroContainer = document.querySelector('.hero-slider-container');
        heroContainer.addEventListener('mouseenter', () => {
            heroAutoAdvance = false;
        });

        heroContainer.addEventListener('mouseleave', () => {
            heroAutoAdvance = true;
        });

        // Enhanced animations on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('slide-in-bottom');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.article-card-hover').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
