<template>
  <div id="app">
    <Header />
    <main>
      <HeroSection />
      <ArticleGrid />
    </main>
    <Footer />
  </div>
</template>

<script>
import Header from './Header.vue';
import HeroSection from './HeroSection.vue';
import ArticleGrid from './ArticleGrid.vue';
import Footer from './Footer.vue';

export default {
  name: 'App',
  components: {
    Header,
    HeroSection,
    ArticleGrid,
    Footer
  }
}
</script>

<style>
/* Global styles will be handled by Tailwind CSS */
</style>
