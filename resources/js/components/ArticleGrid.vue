<template>
  <section class="bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section headers -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        <div v-for="section in sections" :key="section.name" class="border-b border-gray-300 pb-4">
          <h2 class="text-xl font-serif font-bold text-black mb-4">
            {{ section.name }}
          </h2>
          <div class="space-y-4">
            <article v-for="article in section.articles" :key="article.id" class="group cursor-pointer">
              <div class="flex space-x-3">
                <img 
                  :src="article.image" 
                  :alt="article.title"
                  class="w-24 h-24 object-cover flex-shrink-0"
                />
                <div class="flex-1 min-w-0">
                  <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                    {{ article.title }}
                  </h3>
                  <p class="text-xs text-gray-600 mb-1">
                    {{ article.excerpt }}
                  </p>
                  <div class="flex items-center space-x-2 text-xs text-gray-500">
                    <span>{{ article.author }}</span>
                    <span>•</span>
                    <span>{{ article.publishedAt }}</span>
                  </div>
                </div>
              </div>
            </article>
          </div>
        </div>
      </div>

      <!-- Featured articles grid -->
      <div class="border-t border-gray-300 pt-12">
        <h2 class="text-2xl font-serif font-bold text-black mb-8 text-center">
          More Stories
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <article v-for="article in featuredArticles" :key="article.id" 
                   class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden group cursor-pointer">
            <div class="aspect-w-16 aspect-h-9">
              <img 
                :src="article.image" 
                :alt="article.title"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
              />
            </div>
            <div class="p-4">
              <div class="flex items-center space-x-2 text-xs text-gray-600 mb-2">
                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
                  {{ article.category }}
                </span>
                <span>{{ article.publishedAt }}</span>
              </div>
              <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-2">
                {{ article.title }}
              </h3>
              <p class="text-xs text-gray-600 mb-3">
                {{ article.excerpt }}
              </p>
              <div class="flex items-center justify-between text-xs text-gray-500">
                <span>{{ article.author }}</span>
                <span>{{ article.readTime }} min</span>
              </div>
            </div>
          </article>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'ArticleGrid',
  data() {
    return {
      sections: [
        {
          name: 'World',
          articles: [
            {
              id: 8,
              title: 'International Trade Agreements Reshape Global Economy',
              excerpt: 'New partnerships emerge as nations seek economic stability.',
              author: 'Anna Martinez',
              publishedAt: '4 hours ago',
              image: 'https://images.unsplash.com/photo-1526304640581-d334cdbbf45e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 9,
              title: 'Diplomatic Relations Strengthen Between Key Nations',
              excerpt: 'Historic meetings pave way for future cooperation.',
              author: 'Robert Kim',
              publishedAt: '6 hours ago',
              image: 'https://images.unsplash.com/photo-1529107386315-e1a2ed48a620?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            }
          ]
        },
        {
          name: 'Business',
          articles: [
            {
              id: 10,
              title: 'Tech Giants Report Record Quarterly Earnings',
              excerpt: 'Innovation drives unprecedented growth across sectors.',
              author: 'Lisa Thompson',
              publishedAt: '3 hours ago',
              image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 11,
              title: 'Sustainable Energy Investments Reach New Heights',
              excerpt: 'Green technology attracts billions in funding.',
              author: 'David Park',
              publishedAt: '5 hours ago',
              image: 'https://images.unsplash.com/photo-1466611653911-95081537e5b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            }
          ]
        },
        {
          name: 'Science',
          articles: [
            {
              id: 12,
              title: 'Breakthrough in Quantum Computing Research',
              excerpt: 'Scientists achieve new milestone in quantum supremacy.',
              author: 'Dr. Rachel Green',
              publishedAt: '7 hours ago',
              image: 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            },
            {
              id: 13,
              title: 'Medical Advances Promise Better Treatment Options',
              excerpt: 'New therapies show promising results in clinical trials.',
              author: 'Dr. Mark Johnson',
              publishedAt: '8 hours ago',
              image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
            }
          ]
        }
      ],
      featuredArticles: [
        {
          id: 14,
          title: 'The Rise of Artificial Intelligence in Healthcare',
          excerpt: 'How AI is transforming medical diagnosis and treatment.',
          author: 'Dr. Sarah Wilson',
          category: 'Health',
          publishedAt: '1 day ago',
          readTime: 8,
          image: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
        },
        {
          id: 15,
          title: 'Climate Change Solutions Gain Momentum Worldwide',
          excerpt: 'Innovative approaches to environmental challenges show promise.',
          author: 'Environmental Team',
          category: 'Environment',
          publishedAt: '1 day ago',
          readTime: 6,
          image: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
        },
        {
          id: 16,
          title: 'Sports Technology Revolutionizes Athletic Performance',
          excerpt: 'Advanced analytics and wearables change the game.',
          author: 'Sports Desk',
          category: 'Sports',
          publishedAt: '2 days ago',
          readTime: 5,
          image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
        },
        {
          id: 17,
          title: 'Cultural Renaissance in Digital Art Forms',
          excerpt: 'Artists embrace technology to create new expressions.',
          author: 'Arts & Culture',
          category: 'Arts',
          publishedAt: '2 days ago',
          readTime: 7,
          image: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
        }
      ]
    }
  }
}
</script>
