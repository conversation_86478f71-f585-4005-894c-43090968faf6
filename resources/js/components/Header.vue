<template>
  <header class="border-b border-gray-200">
    <!-- Top bar with date and weather -->
    <div class="bg-gray-50 border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-2 text-sm text-gray-600">
          <div class="flex items-center space-x-4">
            <span>{{ currentDate }}</span>
            <span class="hidden sm:inline">Today's Paper</span>
          </div>
          <div class="flex items-center space-x-4">
            <span class="hidden md:inline">Subscribe</span>
            <span>Log In</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Main header -->
    <div class="bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <!-- Mobile menu button -->
          <button @click="toggleMobileMenu" class="md:hidden p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>

          <!-- Logo -->
          <div class="flex-1 flex justify-center md:justify-start">
            <h1 class="text-3xl md:text-4xl font-serif font-bold text-black tracking-tight">
              The Notice Board
            </h1>
          </div>

          <!-- Search and subscribe -->
          <div class="hidden md:flex items-center space-x-4">
            <button class="p-2 hover:bg-gray-100 rounded">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>
            <button class="bg-blue-600 text-white px-4 py-2 text-sm font-medium rounded hover:bg-blue-700">
              Subscribe
            </button>
          </div>
        </div>

        <!-- Navigation -->
        <nav class="border-t border-gray-200">
          <div class="flex justify-center">
            <div class="flex space-x-8 overflow-x-auto py-3">
              <a v-for="item in navigation" :key="item.name" 
                 :href="item.href" 
                 class="text-sm font-medium text-gray-900 hover:text-blue-600 whitespace-nowrap">
                {{ item.name }}
              </a>
            </div>
          </div>
        </nav>
      </div>
    </div>

    <!-- Mobile menu -->
    <div v-if="mobileMenuOpen" class="md:hidden bg-white border-b border-gray-200">
      <div class="px-4 py-3 space-y-3">
        <a v-for="item in navigation" :key="item.name" 
           :href="item.href" 
           class="block text-base font-medium text-gray-900 hover:text-blue-600">
          {{ item.name }}
        </a>
      </div>
    </div>
  </header>
</template>

<script>
export default {
  name: 'Header',
  data() {
    return {
      mobileMenuOpen: false,
      navigation: [
        { name: 'World', href: '#' },
        { name: 'U.S.', href: '#' },
        { name: 'Politics', href: '#' },
        { name: 'Business', href: '#' },
        { name: 'Technology', href: '#' },
        { name: 'Science', href: '#' },
        { name: 'Health', href: '#' },
        { name: 'Sports', href: '#' },
        { name: 'Arts', href: '#' },
        { name: 'Opinion', href: '#' }
      ]
    }
  },
  computed: {
    currentDate() {
      return new Date().toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  },
  methods: {
    toggleMobileMenu() {
      this.mobileMenuOpen = !this.mobileMenuOpen;
    }
  }
}
</script>
