<template>
  <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8">
      <!-- Main Hero Slider -->
      <div class="lg:col-span-3">
        <div class="hero-slider-container">
          <div ref="heroSlider" class="hero-slider">
            <div
              v-for="(slide, index) in heroSlides"
              :key="slide.id"
              class="hero-slide"
              :style="{ backgroundImage: `url(${slide.image})` }"
            >
              <div class="hero-slide-overlay">
                <span class="hero-slide-category">{{ slide.category }}</span>
                <h1 class="hero-slide-title font-serif">
                  {{ slide.title }}
                </h1>
                <p class="hero-slide-excerpt">
                  {{ slide.excerpt }}
                </p>
                <div class="hero-slide-meta">
                  By {{ slide.author }} • {{ slide.publishedAt }} • {{ slide.readTime }} min read
                </div>
              </div>
            </div>
          </div>

          <!-- Navigation Controls -->
          <button @click="prevSlide" class="hero-nav-btn prev">
            <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <button @click="nextSlide" class="hero-nav-btn next">
            <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>

          <!-- Slide Indicators -->
          <div class="hero-indicators">
            <button
              v-for="(slide, index) in heroSlides"
              :key="`indicator-${index}`"
              @click="goToSlide(index)"
              class="hero-indicator"
              :class="{ active: index === currentSlide }"
            ></button>
          </div>
        </div>
      </div>

      <!-- Sidebar stories -->
      <div class="space-y-6">
        <div class="border-b border-gray-200 pb-6">
          <h2 class="text-lg font-serif font-bold text-black mb-4 border-b border-black pb-2">
            Latest News
          </h2>
          <div class="space-y-4">
            <article v-for="story in sidebarStories" :key="story.id" class="group cursor-pointer">
              <div class="flex space-x-3">
                <img 
                  :src="story.image" 
                  :alt="story.title"
                  class="w-20 h-20 object-cover flex-shrink-0"
                />
                <div class="flex-1 min-w-0">
                  <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                    {{ story.title }}
                  </h3>
                  <p class="text-xs text-gray-600">
                    {{ story.publishedAt }}
                  </p>
                </div>
              </div>
            </article>
          </div>
        </div>

        <!-- Opinion section -->
        <div>
          <h2 class="text-lg font-serif font-bold text-black mb-4 border-b border-black pb-2">
            Opinion
          </h2>
          <div class="space-y-3">
            <article v-for="opinion in opinionPieces" :key="opinion.id" class="group cursor-pointer">
              <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                {{ opinion.title }}
              </h3>
              <p class="text-xs text-gray-600">
                By {{ opinion.author }}
              </p>
            </article>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'HeroSection',
  data() {
    return {
      currentSlide: 0,
      autoAdvance: true,
      autoAdvanceInterval: null,
      heroSlides: [
        {
          id: 1,
          title: 'Revolutionary AI Breakthrough Changes Computing Forever',
          excerpt: 'Scientists unveil quantum-AI hybrid system that promises to solve complex problems in seconds, marking a new era in computational power and artificial intelligence.',
          author: 'Dr. Sarah Chen',
          category: 'Technology',
          publishedAt: '2 hours ago',
          readTime: 8,
          image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
        },
        {
          id: 2,
          title: 'Global Climate Summit Reaches Historic Agreement',
          excerpt: 'World leaders unite on unprecedented climate action plan, committing to carbon neutrality by 2040 and massive renewable energy investments.',
          author: 'Environmental Desk',
          category: 'Environment',
          publishedAt: '4 hours ago',
          readTime: 6,
          image: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
        },
        {
          id: 3,
          title: 'Markets Soar as Tech Giants Report Record Earnings',
          excerpt: 'Major technology companies exceed expectations with unprecedented quarterly results, driving global markets to new heights amid economic uncertainty.',
          author: 'Financial Team',
          category: 'Business',
          publishedAt: '5 hours ago',
          readTime: 7,
          image: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
        },
        {
          id: 4,
          title: 'Space Mission Discovers Signs of Ancient Life',
          excerpt: 'NASA\'s latest Mars rover uncovers compelling evidence of microbial life that existed billions of years ago, reshaping our understanding of life in the universe.',
          author: 'Space Correspondent',
          category: 'Science',
          publishedAt: '6 hours ago',
          readTime: 9,
          image: 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
        },
        {
          id: 5,
          title: 'Medical Breakthrough Offers Hope for Cancer Patients',
          excerpt: 'Revolutionary gene therapy shows 95% success rate in clinical trials, offering new hope for patients with previously untreatable forms of cancer.',
          author: 'Medical Correspondent',
          category: 'Health',
          publishedAt: '8 hours ago',
          readTime: 10,
          image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
        }
      ],
      sidebarStories: [
        {
          id: 2,
          title: 'Global Markets React to Economic Policy Changes',
          publishedAt: '3 hours ago',
          image: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
        },
        {
          id: 3,
          title: 'Climate Summit Reaches Historic Agreement',
          publishedAt: '5 hours ago',
          image: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
        },
        {
          id: 4,
          title: 'Space Exploration Mission Achieves New Milestone',
          publishedAt: '6 hours ago',
          image: 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
        }
      ],
      opinionPieces: [
        {
          id: 5,
          title: 'The Future of Work in a Digital Age',
          author: 'Michael Chen'
        },
        {
          id: 6,
          title: 'Why Education Reform Cannot Wait',
          author: 'Dr. Emily Rodriguez'
        },
        {
          id: 7,
          title: 'The Path Forward for Healthcare',
          author: 'James Wilson'
        }
      ]
    }
  },
  mounted() {
    this.startAutoAdvance();
    this.setupHoverEvents();
  },
  beforeUnmount() {
    this.stopAutoAdvance();
  },
  methods: {
    updateSlider() {
      const translateX = -this.currentSlide * 100;
      this.$refs.heroSlider.style.transform = `translateX(${translateX}%)`;
    },
    nextSlide() {
      this.currentSlide = (this.currentSlide + 1) % this.heroSlides.length;
      this.updateSlider();
    },
    prevSlide() {
      this.currentSlide = (this.currentSlide - 1 + this.heroSlides.length) % this.heroSlides.length;
      this.updateSlider();
    },
    goToSlide(index) {
      this.currentSlide = index;
      this.updateSlider();
    },
    startAutoAdvance() {
      this.autoAdvanceInterval = setInterval(() => {
        if (this.autoAdvance) {
          this.nextSlide();
        }
      }, 7000);
    },
    stopAutoAdvance() {
      if (this.autoAdvanceInterval) {
        clearInterval(this.autoAdvanceInterval);
        this.autoAdvanceInterval = null;
      }
    },
    setupHoverEvents() {
      this.$nextTick(() => {
        const container = this.$el.querySelector('.hero-slider-container');
        if (container) {
          container.addEventListener('mouseenter', () => {
            this.autoAdvance = false;
          });
          container.addEventListener('mouseleave', () => {
            this.autoAdvance = true;
          });
        }
      });
    }
  }
}
</script>

<style scoped>
/* Main Hero Slider Styles */
.hero-slider-container {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.hero-slider {
  display: flex;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.hero-slide {
  min-width: 100%;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  aspect-ratio: 16/10;
}

.hero-slide-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 2rem;
  color: white;
}

.hero-slide-category {
  display: inline-block;
  background: #dc2626;
  color: white;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  border-radius: 0.25rem;
  margin-bottom: 0.75rem;
}

.hero-slide-title {
  font-size: 1.875rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 0.75rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-slide-excerpt {
  font-size: 1rem;
  line-height: 1.5;
  opacity: 0.9;
  margin-bottom: 0.75rem;
}

.hero-slide-meta {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Navigation Controls */
.hero-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.hero-nav-btn:hover {
  background: white;
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.hero-nav-btn.prev {
  left: 1rem;
}

.hero-nav-btn.next {
  right: 1rem;
}

/* Slide Indicators */
.hero-indicators {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  z-index: 10;
}

.hero-indicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hero-indicator.active {
  background: white;
  transform: scale(1.2);
}

.hero-indicator:hover {
  background: rgba(255, 255, 255, 0.8);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .hero-slide-title {
    font-size: 1.75rem;
  }

  .hero-slide-overlay {
    padding: 1.75rem;
  }
}

@media (max-width: 768px) {
  .hero-slide-title {
    font-size: 1.5rem;
  }

  .hero-slide-overlay {
    padding: 1.5rem;
  }

  .hero-nav-btn {
    width: 2.5rem;
    height: 2.5rem;
  }

  .hero-nav-btn.prev {
    left: 0.5rem;
  }

  .hero-nav-btn.next {
    right: 0.5rem;
  }

  .hero-slide-excerpt {
    font-size: 0.875rem;
    line-height: 1.4;
  }

  .hero-slide-meta {
    font-size: 0.75rem;
  }
}

@media (max-width: 640px) {
  .hero-slide-title {
    font-size: 1.25rem;
    line-height: 1.3;
  }

  .hero-slide-overlay {
    padding: 1rem;
  }

  .hero-slide-excerpt {
    display: none;
  }

  .hero-nav-btn {
    width: 2rem;
    height: 2rem;
  }

  .hero-indicators {
    bottom: 0.5rem;
  }

  .hero-indicator {
    width: 0.5rem;
    height: 0.5rem;
  }
}
</style>
