<template>
  <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main story -->
      <div class="lg:col-span-2">
        <article class="border-b border-gray-200 pb-8">
          <div class="mb-4">
            <img 
              :src="mainStory.image" 
              :alt="mainStory.title"
              class="w-full h-64 md:h-80 object-cover"
            />
          </div>
          <div class="space-y-4">
            <div class="flex items-center space-x-2 text-sm text-gray-600">
              <span class="bg-red-600 text-white px-2 py-1 text-xs font-medium uppercase">
                {{ mainStory.category }}
              </span>
              <span>{{ mainStory.publishedAt }}</span>
            </div>
            <h1 class="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-black leading-tight">
              {{ mainStory.title }}
            </h1>
            <p class="text-lg md:text-xl text-gray-700 leading-relaxed">
              {{ mainStory.excerpt }}
            </p>
            <div class="flex items-center space-x-4 text-sm text-gray-600">
              <span>By {{ mainStory.author }}</span>
              <span>{{ mainStory.readTime }} min read</span>
            </div>
          </div>
        </article>
      </div>

      <!-- Sidebar stories -->
      <div class="space-y-6">
        <div class="border-b border-gray-200 pb-6">
          <h2 class="text-lg font-serif font-bold text-black mb-4 border-b border-black pb-2">
            Latest News
          </h2>
          <div class="space-y-4">
            <article v-for="story in sidebarStories" :key="story.id" class="group cursor-pointer">
              <div class="flex space-x-3">
                <img 
                  :src="story.image" 
                  :alt="story.title"
                  class="w-20 h-20 object-cover flex-shrink-0"
                />
                <div class="flex-1 min-w-0">
                  <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                    {{ story.title }}
                  </h3>
                  <p class="text-xs text-gray-600">
                    {{ story.publishedAt }}
                  </p>
                </div>
              </div>
            </article>
          </div>
        </div>

        <!-- Opinion section -->
        <div>
          <h2 class="text-lg font-serif font-bold text-black mb-4 border-b border-black pb-2">
            Opinion
          </h2>
          <div class="space-y-3">
            <article v-for="opinion in opinionPieces" :key="opinion.id" class="group cursor-pointer">
              <h3 class="text-sm font-medium text-black group-hover:text-blue-600 leading-tight mb-1">
                {{ opinion.title }}
              </h3>
              <p class="text-xs text-gray-600">
                By {{ opinion.author }}
              </p>
            </article>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'HeroSection',
  data() {
    return {
      mainStory: {
        id: 1,
        title: 'Breaking: Major Technology Breakthrough Changes Everything We Know',
        excerpt: 'Scientists have made a groundbreaking discovery that could revolutionize the way we understand technology and its impact on society. This development promises to reshape industries and create new opportunities for innovation.',
        author: 'Sarah Johnson',
        category: 'Technology',
        publishedAt: '2 hours ago',
        readTime: 5,
        image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
      },
      sidebarStories: [
        {
          id: 2,
          title: 'Global Markets React to Economic Policy Changes',
          publishedAt: '3 hours ago',
          image: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
        },
        {
          id: 3,
          title: 'Climate Summit Reaches Historic Agreement',
          publishedAt: '5 hours ago',
          image: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
        },
        {
          id: 4,
          title: 'Space Exploration Mission Achieves New Milestone',
          publishedAt: '6 hours ago',
          image: 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
        }
      ],
      opinionPieces: [
        {
          id: 5,
          title: 'The Future of Work in a Digital Age',
          author: 'Michael Chen'
        },
        {
          id: 6,
          title: 'Why Education Reform Cannot Wait',
          author: 'Dr. Emily Rodriguez'
        },
        {
          id: 7,
          title: 'The Path Forward for Healthcare',
          author: 'James Wilson'
        }
      ]
    }
  }
}
</script>
